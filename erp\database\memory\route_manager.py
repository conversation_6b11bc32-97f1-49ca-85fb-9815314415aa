"""
Route management for database memory registry
"""
import asyncio
import inspect
import os
import sys
import importlib
from pathlib import Path
from typing import Dict, Optional, Any, List
from ...logging import get_logger


class RouteManager:
    """Manages route registration and discovery for database registry"""

    def __init__(self, db_name: str):
        self.db_name = db_name
        self.routes: Dict[str, Dict] = {}  # Store HTTP routes for this database
        self._routes_registered = False
        self._lock = asyncio.Lock()
        self._logger = get_logger(f"{__name__}.{db_name}")



    async def discover_routes_from_loaded_addons(self, loaded_addons: List[str]) -> None:
        """
        Discover and register routes from a specific list of loaded addons.
        This is the preferred method for unified addon loading.
        """
        if self._routes_registered and len(self.routes) > 0:
            self._logger.debug(f"Routes already registered for {self.db_name}")
            return

        async with self._lock:
            # Double-check after acquiring lock
            if self._routes_registered and len(self.routes) > 0:
                return

            try:
                # Clear existing routes if re-registering
                if self._routes_registered:
                    self.routes.clear()
                    self._routes_registered = False
                    self._logger.debug(f"Clearing routes for re-registration: {self.db_name}")

                # Discover routes from the specific loaded addons
                route_count = 0
                for addon_name in loaded_addons:
                    route_count += await self._discover_addon_routes_for_addon(addon_name)

                # Register routes with FastAPI
                await self._register_with_fastapi()

                self._routes_registered = True
                if route_count > 0:
                    self._logger.info(f"✅ Routes registered for {self.db_name}: {route_count} routes from {len(loaded_addons)} addons")
                else:
                    self._logger.debug(f"No routes found for {self.db_name} from {len(loaded_addons)} addons")

            except Exception as e:
                self._logger.error(f"Route discovery from loaded addons failed for {self.db_name}: {e}")
                raise

    async def _register_with_fastapi(self) -> None:
        """Register discovered routes with FastAPI application"""
        try:
            from .registry_manager import MemoryRegistryManager
            from ...http.integration.fastapi import RouteIntegration

            app = getattr(MemoryRegistryManager, '_app', None)
            if not app:
                self._logger.debug(f"No FastAPI app available for {self.db_name}")
                return

            # Use internal method to get routes without acquiring lock (since lock is already held)
            routes = self.routes.copy()
            if routes:
                RouteIntegration._register_routes_from_registry(app, routes, f"db_{self.db_name}")
                self._logger.info(f"Registered {len(routes)} routes with FastAPI for {self.db_name}")

        except Exception as e:
            self._logger.error(f"FastAPI registration failed for {self.db_name}: {e}")



    async def _discover_addon_routes_for_addon(self, addon_name: str) -> int:
        """Discover routes for a specific addon by scanning all Python modules"""
        route_count = 0
        try:
            # Check main addon module
            main_module_name = f'erp.addons.{addon_name}'
            route_count += self._extract_routes_from_module_name(main_module_name)

            # Scan all Python modules within the addon directory
            route_count += await self._discover_all_addon_modules(addon_name)

        except Exception as e:
            self._logger.warning(f"Failed to discover routes for addon {addon_name}: {e}")

        return route_count

    async def _discover_all_addon_modules(self, addon_name: str) -> int:
        """Discover routes from all Python modules within an addon directory"""
        route_count = 0
        try:
            # First, scan loaded modules that match the addon pattern
            addon_prefix = f'erp.addons.{addon_name}.'
            for module_name in sys.modules:
                if (module_name.startswith(addon_prefix) and
                    module_name != f'erp.addons.{addon_name}'):
                    module = sys.modules[module_name]
                    if module:
                        discovered = self._extract_routes_from_module(module)
                        route_count += discovered

            # Then, scan filesystem for any unloaded modules
            route_count += await self._discover_filesystem_modules(addon_name)

        except Exception as e:
            self._logger.warning(f"Failed to discover addon modules for {addon_name}: {e}")

        return route_count

    async def _discover_filesystem_modules(self, addon_name: str) -> int:
        """Discover and import Python modules from addon filesystem"""
        route_count = 0
        try:
            # Get addon path from import manager
            from ...addons import _addon_import_manager
            addon_paths = _addon_import_manager.list_registered_addons()

            if addon_name not in addon_paths:
                # Try common addon paths
                possible_paths = [
                    os.path.join('addons', addon_name),
                    os.path.join('erp', 'addons', addon_name)
                ]
                addon_path = None
                for path in possible_paths:
                    if os.path.exists(path) and os.path.isdir(path):
                        addon_path = path
                        break

                if not addon_path:
                    self._logger.debug(f"Addon path not found for {addon_name}")
                    return 0
            else:
                addon_path = addon_paths[addon_name]

            # Scan all Python files in the addon directory
            addon_dir = Path(addon_path)
            if not addon_dir.exists():
                self._logger.debug(f"Addon directory does not exist: {addon_path}")
                return 0

            for py_file in addon_dir.rglob("*.py"):
                if py_file.name.startswith('__'):
                    continue  # Skip __init__.py, __manifest__.py, etc.

                # Convert file path to module name
                relative_path = py_file.relative_to(addon_dir)
                module_parts = list(relative_path.parts[:-1]) + [relative_path.stem]
                module_name = f'erp.addons.{addon_name}.' + '.'.join(module_parts)

                # Skip if already processed
                if module_name in sys.modules:
                    continue

                # Try to import and scan the module
                try:
                    importlib.import_module(module_name)
                    if module_name in sys.modules:
                        module = sys.modules[module_name]
                        discovered = self._extract_routes_from_module(module)
                        route_count += discovered
                except ImportError as e:
                    self._logger.debug(f"Could not import module {module_name}: {e}")
                except Exception as e:
                    self._logger.debug(f"Error processing module {module_name}: {e}")

        except Exception as e:
            self._logger.warning(f"Failed to discover filesystem modules for {addon_name}: {e}")

        return route_count



    def _extract_routes_from_module_name(self, module_name: str) -> int:
        """Extract routes from a module by name"""
        if module_name in sys.modules:
            module = sys.modules[module_name]
            return self._extract_routes_from_module(module)
        return 0

    def _extract_routes_from_module(self, module) -> int:
        """Extract routes from a module"""
        route_count = 0

        # Extract function-based routes
        if hasattr(module, '_route_handlers'):
            for handler in module._route_handlers:
                if hasattr(handler, '_route_metadata'):
                    route_metadata = handler._route_metadata
                    path = route_metadata['path']

                    # Register the route for this database (using internal method since lock is already held)
                    self._register_route_internal(path, handler, **{
                        k: v for k, v in route_metadata.items()
                        if k not in ['path', 'original_func']
                    })
                    route_count += 1

        # Extract class-based routes from controller classes
        route_count += self._extract_routes_from_controllers(module)

        return route_count

    def _extract_routes_from_controllers(self, module) -> int:
        """Extract routes from controller classes in a module"""
        route_count = 0

        try:
            # Look for controller classes in the module
            for name, obj in inspect.getmembers(module, inspect.isclass):
                # Check if it's a controller class
                try:
                    from ...http.controllers.base import BaseController
                    if not issubclass(obj, BaseController):
                        continue
                except (ImportError, TypeError):
                    # Skip if we can't import BaseController or obj is not a class
                    continue

                # Extract routes from controller methods
                for method_name, method in inspect.getmembers(obj, inspect.isfunction):
                    if hasattr(method, '_route_metadata'):
                        route_metadata = method._route_metadata
                        path = route_metadata['path']

                        # Create a bound method handler that will work with the controller instance
                        # We'll need to create the controller instance when the route is called
                        controller_handler = self._create_controller_handler(obj, method, route_metadata)

                        # Register the route for this database
                        self._register_route_internal(path, controller_handler, **{
                            k: v for k, v in route_metadata.items()
                            if k not in ['path', 'original_func']
                        })
                        route_count += 1
                        self._logger.debug(f"Found controller route: {obj.__name__}.{method_name} -> {path}")

        except Exception as e:
            self._logger.debug(f"Error extracting routes from controllers in module {getattr(module, '__name__', 'unknown')}: {e}")

        return route_count

    def _create_controller_handler(self, controller_class, method, route_metadata):
        """Create a handler function that instantiates the controller and calls the method"""
        import functools

        @functools.wraps(method)
        async def controller_handler(*args, **kwargs):
            # Create controller instance
            controller_instance = controller_class()

            # Call the method on the controller instance
            if inspect.iscoroutinefunction(method):
                return await method(controller_instance, *args, **kwargs)
            else:
                return method(controller_instance, *args, **kwargs)

        # Copy route metadata to the handler
        controller_handler._route_metadata = route_metadata
        controller_handler._controller_class = controller_class
        controller_handler._controller_method = method

        return controller_handler

    def _is_addon_module(self, module_name: str) -> bool:
        """Check if module name is an addon module"""
        return module_name.startswith('erp.addons.') and module_name != 'erp.addons'

    async def register_route(self, path: str, handler: Any, **kwargs) -> None:
        """Register a route handler for this database"""
        async with self._lock:
            self._register_route_internal(path, handler, **kwargs)

    def _register_route_internal(self, path: str, handler: Any, **kwargs) -> None:
        """Internal route registration without lock (for use when lock is already held)"""
        route_info = {
            'handler': handler,
            'path': path,
            **kwargs
        }
        self.routes[path] = route_info

    async def get_routes(self) -> Dict[str, Dict]:
        """Get all registered routes for this database"""
        # Don't call ensure_routes_registered here to avoid circular dependency
        async with self._lock:
            return self.routes.copy()

    async def get_route(self, path: str) -> Optional[Dict]:
        """Get specific route by path"""
        async with self._lock:
            return self.routes.get(path)

    async def remove_route(self, path: str) -> bool:
        """Remove a route by path"""
        async with self._lock:
            if path in self.routes:
                del self.routes[path]
                self._logger.debug(f"Removed route for {self.db_name}: {path}")
                return True
            return False

    async def clear_routes(self) -> None:
        """Clear all routes for this database"""
        async with self._lock:
            self.routes.clear()
            self._logger.debug(f"Cleared all routes for {self.db_name}")

    def get_route_stats(self) -> Dict[str, Any]:
        """Get route statistics"""
        return {
            'routes_count': len(self.routes),
            'routes_registered': self._routes_registered
        }

    def reset_route_state(self) -> None:
        """Reset route registration state"""
        self._routes_registered = False
        self.routes.clear()
        self._logger.debug("Route state reset")
